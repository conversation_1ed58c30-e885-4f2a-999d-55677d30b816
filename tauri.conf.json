{"package": {"productName": "POS System", "version": "0.1.0"}, "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "distDir": "../dist", "devPath": "http://localhost:1420"}, "tauri": {"bundle": {"active": true, "targets": "all", "identifier": "com.posystem.app", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "windows": [{"fullscreen": false, "resizable": true, "title": "POS System", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}], "security": {"csp": null}, "allowlist": {"all": false, "shell": {"all": false, "open": true}, "fs": {"all": false, "scope": ["$APPDATA", "$APPDATA/**", "$RESOURCE", "$RESOURCE/**"]}, "http": {"all": false, "request": true, "scope": ["https://**", "http://**"]}, "notification": {"all": true}, "dialog": {"all": true}}}}