{"hash": "2d6db2da", "configHash": "d8c5e705", "lockfileHash": "979f4830", "browserHash": "2702e0f8", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "a347f64e", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "0370d456", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "0a47b33a", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "88e7e4bd", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "6556cb20", "needsInterop": false}, "@tauri-apps/api/event": {"src": "../../@tauri-apps/api/event.js", "file": "@tauri-apps_api_event.js", "fileHash": "83512238", "needsInterop": false}, "@tauri-apps/api/tauri": {"src": "../../@tauri-apps/api/tauri.js", "file": "@tauri-apps_api_tauri.js", "fileHash": "08592f2c", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "8c47f353", "needsInterop": false}, "date-fns": {"src": "../../date-fns/esm/index.js", "file": "date-fns.js", "fileHash": "13fc70d9", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "06e6c0bb", "needsInterop": true}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "007c2599", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "e0ee3251", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "fc236271", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "26c3<PERSON><PERSON>", "needsInterop": false}}, "chunks": {"chunk-TYILIMWK": {"file": "chunk-TYILIMWK.js"}, "chunk-URXWTKVS": {"file": "chunk-URXWTKVS.js"}, "chunk-CANBAPAS": {"file": "chunk-CANBAPAS.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}