import { ReactNode, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  ShoppingCartIcon,
  CubeIcon,
  ChartBarIcon,
  CogIcon,
  WifiIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { usePOSStore } from '../store/posStore';
import { invoke } from '@tauri-apps/api/tauri';
import { SyncStatus } from '../types';
import clsx from 'clsx';

interface LayoutProps {
  children: ReactNode;
}

const navigation = [
  { name: 'Dashboard', href: '/', icon: HomeIcon },
  { name: 'POS', href: '/pos', icon: ShoppingCartIcon },
  { name: 'Products', href: '/products', icon: CubeIcon },
  { name: 'Sales', href: '/sales', icon: ChartBarIcon },
  { name: 'Settings', href: '/settings', icon: CogIcon },
];

export default function Layout({ children }: LayoutProps) {
  const location = useLocation();
  const { syncStatus, setSyncStatus, loadProducts } = usePOSStore();

  useEffect(() => {
    // Load initial data
    loadProducts();
    
    // Get initial sync status
    const getSyncStatus = async () => {
      try {
        const status = await invoke<SyncStatus>('get_sync_status');
        setSyncStatus(status);
      } catch (error) {
        console.error('Failed to get sync status:', error);
      }
    };
    
    getSyncStatus();
  }, [loadProducts, setSyncStatus]);

  const handleSync = async () => {
    try {
      const status = await invoke<SyncStatus>('sync_data');
      setSyncStatus(status);
    } catch (error) {
      console.error('Manual sync failed:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg">
        <div className="flex h-16 items-center justify-center border-b border-gray-200">
          <h1 className="text-xl font-bold text-gray-900">POS System</h1>
        </div>
        
        <nav className="mt-8 px-4">
          <ul className="space-y-2">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href;
              return (
                <li key={item.name}>
                  <Link
                    to={item.href}
                    className={clsx(
                      'group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors',
                      isActive
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    )}
                  >
                    <item.icon
                      className={clsx(
                        'mr-3 h-5 w-5 flex-shrink-0',
                        isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                      )}
                    />
                    {item.name}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>
        
        {/* Sync Status */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {syncStatus?.is_online ? (
                <WifiIcon className="h-5 w-5 text-success-500" />
              ) : (
                <ExclamationTriangleIcon className="h-5 w-5 text-warning-500" />
              )}
              <span className="text-sm text-gray-600">
                {syncStatus?.is_online ? 'Online' : 'Offline'}
              </span>
            </div>
            
            <button
              onClick={handleSync}
              className="btn-sm btn-secondary"
              disabled={!syncStatus}
            >
              Sync
            </button>
          </div>
          
          {syncStatus && (
            <div className="mt-2 text-xs text-gray-500">
              <div>Till: {syncStatus.till_id}</div>
              {syncStatus.pending_changes > 0 && (
                <div className="text-warning-600">
                  {syncStatus.pending_changes} pending changes
                </div>
              )}
              {syncStatus.last_sync && (
                <div>
                  Last sync: {new Date(syncStatus.last_sync).toLocaleTimeString()}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      
      {/* Main content */}
      <div className="pl-64">
        <main className="py-8">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}