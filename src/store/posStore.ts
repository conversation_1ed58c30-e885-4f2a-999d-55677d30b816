import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Product, Sale, SyncStatus, CartItem, PaymentMethod, POSState } from '../types';
import { invoke } from '@tauri-apps/api/tauri';
import toast from 'react-hot-toast';

interface POSStore {
  // Products
  products: Product[];
  isLoadingProducts: boolean;
  
  // Sales
  sales: Sale[];
  isLoadingSales: boolean;
  
  // Sync
  syncStatus: SyncStatus | null;
  
  // POS State
  posState: POSState;
  
  // Actions
  loadProducts: () => Promise<void>;
  addProduct: (product: Omit<Product, 'id' | 'created_at' | 'updated_at' | 'is_deleted' | 'last_modified_by'>) => Promise<void>;
  updateProduct: (id: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  
  loadSales: (limit?: number) => Promise<void>;
  createSale: (items: { product_id: string; quantity: number; discount_amount?: number }[], paymentMethod: string, customerDiscount?: number) => Promise<void>;
  
  setSyncStatus: (status: SyncStatus) => void;
  syncData: () => Promise<void>;
  
  // POS Actions
  addToCart: (product: Product, quantity?: number) => void;
  removeFromCart: (productId: string) => void;
  updateCartItemQuantity: (productId: string, quantity: number) => void;
  updateCartItemDiscount: (productId: string, discount: number) => void;
  clearCart: () => void;
  setPaymentMethod: (method: PaymentMethod) => void;
  setCustomerDiscount: (discount: number) => void;
  processCheckout: () => Promise<void>;
}

export const usePOSStore = create<POSStore>()(devtools((set, get) => ({
  // Initial state
  products: [],
  isLoadingProducts: false,
  sales: [],
  isLoadingSales: false,
  syncStatus: null,
  posState: {
    cart: [],
    selectedPaymentMethod: 'cash',
    customerDiscount: 0,
    isProcessing: false,
  },
  
  // Product actions
  loadProducts: async () => {
    set({ isLoadingProducts: true });
    try {
      const products = await invoke<Product[]>('get_products');
      set({ products, isLoadingProducts: false });
    } catch (error) {
      console.error('Failed to load products:', error);
      toast.error('Failed to load products');
      set({ isLoadingProducts: false });
    }
  },
  
  addProduct: async (productData) => {
    try {
      const product = await invoke<Product>('add_product', { request: productData });
      set(state => ({ products: [...state.products, product] }));
      toast.success('Product added successfully');
    } catch (error) {
      console.error('Failed to add product:', error);
      toast.error('Failed to add product');
      throw error;
    }
  },
  
  updateProduct: async (id, updates) => {
    try {
      const updatedProduct = await invoke<Product>('update_product', {
        request: { id, ...updates }
      });
      set(state => ({
        products: state.products.map(p => p.id === id ? updatedProduct : p)
      }));
      toast.success('Product updated successfully');
    } catch (error) {
      console.error('Failed to update product:', error);
      toast.error('Failed to update product');
      throw error;
    }
  },
  
  deleteProduct: async (id) => {
    try {
      await invoke('delete_product', { id });
      set(state => ({
        products: state.products.filter(p => p.id !== id)
      }));
      toast.success('Product deleted successfully');
    } catch (error) {
      console.error('Failed to delete product:', error);
      toast.error('Failed to delete product');
      throw error;
    }
  },
  
  // Sales actions
  loadSales: async (limit) => {
    set({ isLoadingSales: true });
    try {
      const sales = await invoke<Sale[]>('get_sales', { limit });
      set({ sales, isLoadingSales: false });
    } catch (error) {
      console.error('Failed to load sales:', error);
      toast.error('Failed to load sales');
      set({ isLoadingSales: false });
    }
  },
  
  createSale: async (items, paymentMethod, customerDiscount = 0) => {
    try {
      const sale = await invoke<Sale>('create_sale', {
        request: {
          items,
          payment_method: paymentMethod,
          discount_amount: customerDiscount,
        }
      });
      set(state => ({ sales: [sale, ...state.sales] }));
      toast.success('Sale completed successfully');
      
      // Reload products to update stock quantities
      get().loadProducts();
    } catch (error) {
      console.error('Failed to create sale:', error);
      toast.error('Failed to process sale');
      throw error;
    }
  },
  
  // Sync actions
  setSyncStatus: (status) => {
    set({ syncStatus: status });
  },
  
  syncData: async () => {
    try {
      const status = await invoke<SyncStatus>('sync_data');
      set({ syncStatus: status });
      if (status.is_online) {
        toast.success('Data synced successfully');
      }
    } catch (error) {
      console.error('Failed to sync data:', error);
      toast.error('Failed to sync data');
    }
  },
  
  // POS actions
  addToCart: (product, quantity = 1) => {
    set(state => {
      const existingItem = state.posState.cart.find(item => item.product.id === product.id);
      
      if (existingItem) {
        return {
          posState: {
            ...state.posState,
            cart: state.posState.cart.map(item =>
              item.product.id === product.id
                ? { ...item, quantity: item.quantity + quantity }
                : item
            )
          }
        };
      } else {
        return {
          posState: {
            ...state.posState,
            cart: [...state.posState.cart, { product, quantity, discount_amount: 0 }]
          }
        };
      }
    });
  },
  
  removeFromCart: (productId) => {
    set(state => ({
      posState: {
        ...state.posState,
        cart: state.posState.cart.filter(item => item.product.id !== productId)
      }
    }));
  },
  
  updateCartItemQuantity: (productId, quantity) => {
    if (quantity <= 0) {
      get().removeFromCart(productId);
      return;
    }
    
    set(state => ({
      posState: {
        ...state.posState,
        cart: state.posState.cart.map(item =>
          item.product.id === productId
            ? { ...item, quantity }
            : item
        )
      }
    }));
  },
  
  updateCartItemDiscount: (productId, discount) => {
    set(state => ({
      posState: {
        ...state.posState,
        cart: state.posState.cart.map(item =>
          item.product.id === productId
            ? { ...item, discount_amount: discount }
            : item
        )
      }
    }));
  },
  
  clearCart: () => {
    set(state => ({
      posState: {
        ...state.posState,
        cart: [],
        customerDiscount: 0,
      }
    }));
  },
  
  setPaymentMethod: (method) => {
    set(state => ({
      posState: {
        ...state.posState,
        selectedPaymentMethod: method
      }
    }));
  },
  
  setCustomerDiscount: (discount) => {
    set(state => ({
      posState: {
        ...state.posState,
        customerDiscount: discount
      }
    }));
  },
  
  processCheckout: async () => {
    const { posState, createSale, clearCart } = get();
    
    if (posState.cart.length === 0) {
      toast.error('Cart is empty');
      return;
    }
    
    set(state => ({
      posState: { ...state.posState, isProcessing: true }
    }));
    
    try {
      const items = posState.cart.map(item => ({
        product_id: item.product.id,
        quantity: item.quantity,
        discount_amount: item.discount_amount,
      }));
      
      await createSale(items, posState.selectedPaymentMethod, posState.customerDiscount);
      clearCart();
    } catch (error) {
      // Error handling is done in createSale
    } finally {
      set(state => ({
        posState: { ...state.posState, isProcessing: false }
      }));
    }
  },
}), {
  name: 'pos-store',
}));