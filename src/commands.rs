use crate::database::{Database, get_setting, set_setting};
use crate::models::*;
use crate::sync::{SyncManager, log_change};
use anyhow::Result;
use chrono::Utc;
use std::sync::Arc;
use tauri::State;
use tokio::sync::Mutex;
use uuid::Uuid;

type DbState = Arc<Mutex<Database>>;

#[tauri::command]
pub async fn add_product(
    db_state: State<'_, DbState>,
    request: CreateProductRequest,
) -> Result<Product, String> {
    let db = db_state.lock().await;
    
    // Get till ID
    let till_id = get_setting(&*db, "till_id")
        .await
        .map_err(|e| e.to_string())?
        .unwrap_or_else(|| "till_1".to_string());
    
    let product = Product::new(request, till_id.clone());
    
    // Insert product
    crate::database::insert_product(&*db, &product)
        .await
        .map_err(|e| e.to_string())?;
    
    // Log change for sync
    let product_json = serde_json::to_string(&product).map_err(|e| e.to_string())?;
    log_change(&*db, "products", &product.id, "INSERT", &product_json, &till_id)
        .await
        .map_err(|e| e.to_string())?;
    
    Ok(product)
}

#[tauri::command]
pub async fn get_products(db_state: State<'_, DbState>) -> Result<Vec<Product>, String> {
    let db = db_state.lock().await;
    crate::database::get_products(&*db)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn update_product(
    db_state: State<'_, DbState>,
    request: UpdateProductRequest,
) -> Result<Product, String> {
    let db = db_state.lock().await;
    
    // Get till ID
    let till_id = get_setting(&*db, "till_id")
        .await
        .map_err(|e| e.to_string())?
        .unwrap_or_else(|| "till_1".to_string());
    
    // Get existing product
    let products = crate::database::get_products(&*db)
        .await
        .map_err(|e| e.to_string())?;
    
    let mut product = products
        .into_iter()
        .find(|p| p.id == request.id)
        .ok_or("Product not found")?;
    
    // Update fields
    if let Some(name) = request.name {
        product.name = name;
    }
    if let Some(description) = request.description {
        product.description = description;
    }
    if let Some(price) = request.price {
        product.price = price;
    }
    if let Some(stock_quantity) = request.stock_quantity {
        product.stock_quantity = stock_quantity;
    }
    if let Some(barcode) = request.barcode {
        product.barcode = barcode;
    }
    if let Some(category) = request.category {
        product.category = category;
    }
    
    product.updated_at = Utc::now();
    product.last_modified_by = till_id.clone();
    
    // Update product
    crate::database::update_product(&*db, &product)
        .await
        .map_err(|e| e.to_string())?;
    
    // Log change for sync
    let product_json = serde_json::to_string(&product).map_err(|e| e.to_string())?;
    log_change(&*db, "products", &product.id, "UPDATE", &product_json, &till_id)
        .await
        .map_err(|e| e.to_string())?;
    
    Ok(product)
}

#[tauri::command]
pub async fn delete_product(
    db_state: State<'_, DbState>,
    id: String,
) -> Result<(), String> {
    let db = db_state.lock().await;
    
    // Get till ID
    let till_id = get_setting(&*db, "till_id")
        .await
        .map_err(|e| e.to_string())?
        .unwrap_or_else(|| "till_1".to_string());
    
    // Delete product
    crate::database::delete_product(&*db, &id, &till_id)
        .await
        .map_err(|e| e.to_string())?;
    
    // Log change for sync
    log_change(&*db, "products", &id, "DELETE", "{}", &till_id)
        .await
        .map_err(|e| e.to_string())?;
    
    Ok(())
}

#[tauri::command]
pub async fn create_sale(
    db_state: State<'_, DbState>,
    request: CreateSaleRequest,
) -> Result<Sale, String> {
    let db = db_state.lock().await;
    
    // Get till ID
    let till_id = get_setting(&*db, "till_id")
        .await
        .map_err(|e| e.to_string())?
        .unwrap_or_else(|| "till_1".to_string());
    
    // Calculate totals
    let mut total_amount = 0.0;
    let tax_rate = 0.1; // 10% tax
    let discount_amount = request.discount_amount.unwrap_or(0.0);
    
    // Get products to calculate prices
    let products = crate::database::get_products(&*db)
        .await
        .map_err(|e| e.to_string())?;
    
    let mut sale_items = Vec::new();
    
    for item_request in &request.items {
        let product = products
            .iter()
            .find(|p| p.id == item_request.product_id)
            .ok_or("Product not found")?;
        
        let item_discount = item_request.discount_amount.unwrap_or(0.0);
        let item_total = (product.price * item_request.quantity as f64) - item_discount;
        total_amount += item_total;
        
        sale_items.push(SaleItem::new(
            String::new(), // Will be set after sale creation
            product.id.clone(),
            item_request.quantity,
            product.price,
            item_discount,
        ));
    }
    
    total_amount -= discount_amount;
    let tax_amount = total_amount * tax_rate;
    total_amount += tax_amount;
    
    // Create sale
    let sale = Sale::new(
        total_amount,
        tax_amount,
        discount_amount,
        request.payment_method,
        request.customer_id,
        till_id.clone(),
    );
    
    // Insert sale
    crate::database::insert_sale(&*db, &sale)
        .await
        .map_err(|e| e.to_string())?;
    
    // Insert sale items
    for mut item in sale_items {
        item.sale_id = sale.id.clone();
        crate::database::insert_sale_item(&*db, &item)
            .await
            .map_err(|e| e.to_string())?;
        
        // Update product stock
        let mut product = products
            .iter()
            .find(|p| p.id == item.product_id)
            .unwrap()
            .clone();
        
        product.stock_quantity -= item.quantity;
        product.updated_at = Utc::now();
        product.last_modified_by = till_id.clone();
        
        crate::database::update_product(&*db, &product)
            .await
            .map_err(|e| e.to_string())?;
        
        // Log product update for sync
        let product_json = serde_json::to_string(&product).map_err(|e| e.to_string())?;
        log_change(&*db, "products", &product.id, "UPDATE", &product_json, &till_id)
            .await
            .map_err(|e| e.to_string())?;
    }
    
    // Log sale for sync
    let sale_json = serde_json::to_string(&sale).map_err(|e| e.to_string())?;
    log_change(&*db, "sales", &sale.id, "INSERT", &sale_json, &till_id)
        .await
        .map_err(|e| e.to_string())?;
    
    Ok(sale)
}

#[tauri::command]
pub async fn get_sales(
    db_state: State<'_, DbState>,
    limit: Option<i32>,
) -> Result<Vec<Sale>, String> {
    let db = db_state.lock().await;
    crate::database::get_sales(&*db, limit)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_sale_items(
    db_state: State<'_, DbState>,
    sale_id: String,
) -> Result<Vec<SaleItem>, String> {
    let db = db_state.lock().await;
    crate::database::get_sale_items(&*db, &sale_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn sync_data(db_state: State<'_, DbState>) -> Result<SyncStatus, String> {
    let db = db_state.lock().await;
    
    // Get till ID
    let till_id = get_setting(&*db, "till_id")
        .await
        .map_err(|e| e.to_string())?
        .unwrap_or_else(|| "till_1".to_string());
    
    let sync_manager = SyncManager::new(till_id);
    sync_manager
        .sync_data(&*db)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_sync_status(db_state: State<'_, DbState>) -> Result<SyncStatus, String> {
    let db = db_state.lock().await;
    
    // Get till ID
    let till_id = get_setting(&*db, "till_id")
        .await
        .map_err(|e| e.to_string())?
        .unwrap_or_else(|| "till_1".to_string());
    
    let sync_manager = SyncManager::new(till_id.clone());
    
    // Get last sync time
    let last_sync = get_setting(&*db, "last_sync")
        .await
        .map_err(|e| e.to_string())?
        .and_then(|s| s.parse().ok());
    
    // Count pending changes
    let pending_changes = sqlx::query("SELECT COUNT(*) as count FROM sync_logs WHERE is_synced = FALSE")
        .fetch_one(&*db)
        .await
        .map_err(|e| e.to_string())?
        .get::<i32, _>("count");
    
    Ok(SyncStatus {
        is_online: false, // Will be updated by actual sync check
        last_sync,
        pending_changes,
        till_id,
    })
}

#[tauri::command]
pub async fn set_till_id(
    db_state: State<'_, DbState>,
    till_id: String,
) -> Result<(), String> {
    let db = db_state.lock().await;
    set_setting(&*db, "till_id", &till_id)
        .await
        .map_err(|e| e.to_string())
}