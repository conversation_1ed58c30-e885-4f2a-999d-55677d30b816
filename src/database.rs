use sqlx::{SqlitePool, Row};
use anyhow::Result;
use crate::models::*;
use chrono::Utc;
use std::path::PathBuf;

pub type Database = SqlitePool;

pub async fn init_database() -> Result<Database> {
    // Use current directory for now - in a real app you'd use proper app data directory
    let db_path = PathBuf::from("pos_system.db");
    let database_url = format!("sqlite:{}", db_path.display());
    
    let pool = SqlitePool::connect(&database_url).await?;
    
    // Run migrations
    run_migrations(&pool).await?;
    
    Ok(pool)
}

async fn run_migrations(pool: &SqlitePool) -> Result<()> {
    // Create products table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS products (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            price REAL NOT NULL,
            stock_quantity INTEGER NOT NULL,
            barcode TEXT,
            category TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
            last_modified_by TEXT NOT NULL
        )
        "#,
    )
    .execute(pool)
    .await?;
    
    // Create sales table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS sales (
            id TEXT PRIMARY KEY,
            total_amount REAL NOT NULL,
            tax_amount REAL NOT NULL,
            discount_amount REAL NOT NULL,
            payment_method TEXT NOT NULL,
            customer_id TEXT,
            till_id TEXT NOT NULL,
            created_at TEXT NOT NULL,
            is_synced BOOLEAN NOT NULL DEFAULT FALSE
        )
        "#,
    )
    .execute(pool)
    .await?;
    
    // Create sale_items table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS sale_items (
            id TEXT PRIMARY KEY,
            sale_id TEXT NOT NULL,
            product_id TEXT NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            discount_amount REAL NOT NULL,
            FOREIGN KEY (sale_id) REFERENCES sales (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        "#,
    )
    .execute(pool)
    .await?;
    
    // Create sync_logs table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS sync_logs (
            id TEXT PRIMARY KEY,
            table_name TEXT NOT NULL,
            record_id TEXT NOT NULL,
            operation TEXT NOT NULL,
            data TEXT NOT NULL,
            till_id TEXT NOT NULL,
            created_at TEXT NOT NULL,
            is_synced BOOLEAN NOT NULL DEFAULT FALSE
        )
        "#,
    )
    .execute(pool)
    .await?;
    
    // Create settings table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS settings (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL
        )
        "#,
    )
    .execute(pool)
    .await?;
    
    // Create indexes
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)")
        .execute(pool)
        .await?;
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_sales_till_id ON sales(till_id)")
        .execute(pool)
        .await?;
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_sync_logs_synced ON sync_logs(is_synced)")
        .execute(pool)
        .await?;
    
    Ok(())
}

// Product operations
pub async fn insert_product(pool: &Database, product: &Product) -> Result<()> {
    sqlx::query(
        r#"
        INSERT INTO products (id, name, description, price, stock_quantity, barcode, category, created_at, updated_at, is_deleted, last_modified_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        "#,
    )
    .bind(&product.id)
    .bind(&product.name)
    .bind(&product.description)
    .bind(product.price)
    .bind(product.stock_quantity)
    .bind(&product.barcode)
    .bind(&product.category)
    .bind(product.created_at.to_rfc3339())
    .bind(product.updated_at.to_rfc3339())
    .bind(product.is_deleted)
    .bind(&product.last_modified_by)
    .execute(pool)
    .await?;
    
    Ok(())
}

pub async fn get_products(pool: &Database) -> Result<Vec<Product>> {
    let rows = sqlx::query(
        "SELECT id, name, description, price, stock_quantity, barcode, category, created_at, updated_at, is_deleted, last_modified_by FROM products WHERE is_deleted = FALSE"
    )
    .fetch_all(pool)
    .await?;
    
    let mut products = Vec::new();
    for row in rows {
        let created_at: String = sqlx::Row::get(&row, "created_at");
        let updated_at: String = sqlx::Row::get(&row, "updated_at");
        
        products.push(Product {
            id: sqlx::Row::get(&row, "id"),
            name: sqlx::Row::get(&row, "name"),
            description: sqlx::Row::get(&row, "description"),
            price: sqlx::Row::get(&row, "price"),
            stock_quantity: sqlx::Row::get(&row, "stock_quantity"),
            barcode: sqlx::Row::get(&row, "barcode"),
            category: sqlx::Row::get(&row, "category"),
            created_at: created_at.parse().unwrap_or(Utc::now()),
            updated_at: updated_at.parse().unwrap_or(Utc::now()),
            is_deleted: sqlx::Row::get(&row, "is_deleted"),
            last_modified_by: sqlx::Row::get(&row, "last_modified_by"),
        });
    }
    
    Ok(products)
}

pub async fn update_product(pool: &Database, product: &Product) -> Result<()> {
    sqlx::query(
        r#"
        UPDATE products 
        SET name = ?, description = ?, price = ?, stock_quantity = ?, barcode = ?, category = ?, updated_at = ?, last_modified_by = ?
        WHERE id = ?
        "#,
    )
    .bind(&product.name)
    .bind(&product.description)
    .bind(product.price)
    .bind(product.stock_quantity)
    .bind(&product.barcode)
    .bind(&product.category)
    .bind(product.updated_at.to_rfc3339())
    .bind(&product.last_modified_by)
    .bind(&product.id)
    .execute(pool)
    .await?;
    
    Ok(())
}

pub async fn delete_product(pool: &Database, id: &str, till_id: &str) -> Result<()> {
    sqlx::query(
        "UPDATE products SET is_deleted = TRUE, updated_at = ?, last_modified_by = ? WHERE id = ?"
    )
    .bind(Utc::now().to_rfc3339())
    .bind(till_id)
    .bind(id)
    .execute(pool)
    .await?;
    
    Ok(())
}

// Sale operations
pub async fn insert_sale(pool: &Database, sale: &Sale) -> Result<()> {
    sqlx::query(
        r#"
        INSERT INTO sales (id, total_amount, tax_amount, discount_amount, payment_method, customer_id, till_id, created_at, is_synced)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        "#,
    )
    .bind(&sale.id)
    .bind(sale.total_amount)
    .bind(sale.tax_amount)
    .bind(sale.discount_amount)
    .bind(&sale.payment_method)
    .bind(&sale.customer_id)
    .bind(&sale.till_id)
    .bind(sale.created_at.to_rfc3339())
    .bind(sale.is_synced)
    .execute(pool)
    .await?;
    
    Ok(())
}

pub async fn insert_sale_item(pool: &Database, sale_item: &SaleItem) -> Result<()> {
    sqlx::query(
        r#"
        INSERT INTO sale_items (id, sale_id, product_id, quantity, unit_price, total_price, discount_amount)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        "#,
    )
    .bind(&sale_item.id)
    .bind(&sale_item.sale_id)
    .bind(&sale_item.product_id)
    .bind(sale_item.quantity)
    .bind(sale_item.unit_price)
    .bind(sale_item.total_price)
    .bind(sale_item.discount_amount)
    .execute(pool)
    .await?;
    
    Ok(())
}

pub async fn get_sales(pool: &Database, limit: Option<i32>) -> Result<Vec<Sale>> {
    let query = match limit {
        Some(l) => format!("SELECT * FROM sales ORDER BY created_at DESC LIMIT {}", l),
        None => "SELECT * FROM sales ORDER BY created_at DESC".to_string(),
    };
    
    let rows = sqlx::query(&query).fetch_all(pool).await?;
    
    let mut sales = Vec::new();
    for row in rows {
        let created_at: String = sqlx::Row::get(&row, "created_at");
        
        sales.push(Sale {
            id: sqlx::Row::get(&row, "id"),
            total_amount: sqlx::Row::get(&row, "total_amount"),
            tax_amount: sqlx::Row::get(&row, "tax_amount"),
            discount_amount: sqlx::Row::get(&row, "discount_amount"),
            payment_method: sqlx::Row::get(&row, "payment_method"),
            customer_id: sqlx::Row::get(&row, "customer_id"),
            till_id: sqlx::Row::get(&row, "till_id"),
            created_at: created_at.parse().unwrap_or(Utc::now()),
            is_synced: sqlx::Row::get(&row, "is_synced"),
        });
    }
    
    Ok(sales)
}

pub async fn get_sale_items(pool: &Database, sale_id: &str) -> Result<Vec<SaleItem>> {
    let rows = sqlx::query(
        "SELECT * FROM sale_items WHERE sale_id = ?"
    )
    .bind(sale_id)
    .fetch_all(pool)
    .await?;
    
    let mut items = Vec::new();
    for row in rows {
        items.push(SaleItem {
            id: sqlx::Row::get(&row, "id"),
            sale_id: sqlx::Row::get(&row, "sale_id"),
            product_id: sqlx::Row::get(&row, "product_id"),
            quantity: sqlx::Row::get(&row, "quantity"),
            unit_price: sqlx::Row::get(&row, "unit_price"),
            total_price: sqlx::Row::get(&row, "total_price"),
            discount_amount: sqlx::Row::get(&row, "discount_amount"),
        });
    }
    
    Ok(items)
}

// Settings operations
pub async fn get_setting(pool: &Database, key: &str) -> Result<Option<String>> {
    let row = sqlx::query("SELECT value FROM settings WHERE key = ?")
        .bind(key)
        .fetch_optional(pool)
        .await?;
    
    Ok(row.map(|r| sqlx::Row::get(&r, "value")))
}

pub async fn set_setting(pool: &Database, key: &str, value: &str) -> Result<()> {
    sqlx::query(
        "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)"
    )
    .bind(key)
    .bind(value)
    .execute(pool)
    .await?;
    
    Ok(())
}