@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-50 text-gray-900;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300;
  }
  
  .btn-success {
    @apply btn bg-green-600 text-white hover:bg-green-700 active:bg-green-800;
  }
  
  .btn-warning {
    @apply btn bg-yellow-500 text-white hover:bg-yellow-600 active:bg-yellow-700;
  }
  
  .btn-error {
    @apply btn bg-red-600 text-white hover:bg-red-700 active:bg-red-800;
  }
  
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-12 px-6 text-base;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }
  
  .card-description {
    @apply text-sm text-gray-500;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  .table {
    @apply w-full caption-bottom text-sm;
  }
  
  .table-header {
    @apply border-b;
  }
  
  .table-body {
    @apply [&_tr:last-child]:border-0;
  }
  
  .table-footer {
    @apply border-t bg-gray-50/50 font-medium;
  }
  
  .table-row {
    @apply border-b transition-colors hover:bg-gray-50/50 data-[state=selected]:bg-gray-50;
  }
  
  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-gray-500;
  }
  
  .table-cell {
    @apply p-4 align-middle;
  }
  
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
  
  .badge-default {
    @apply badge border-transparent bg-gray-900 text-gray-50 hover:bg-gray-900/80;
  }
  
  .badge-secondary {
    @apply badge border-transparent bg-gray-100 text-gray-900 hover:bg-gray-100/80;
  }
  
  .badge-success {
    @apply badge border-transparent bg-green-500 text-white hover:bg-green-600;
  }
  
  .badge-warning {
    @apply badge border-transparent bg-yellow-500 text-white hover:bg-yellow-600;
  }
  
  .badge-error {
    @apply badge border-transparent bg-red-500 text-white hover:bg-red-600;
  }
  
  .badge-outline {
    @apply badge text-gray-950;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}