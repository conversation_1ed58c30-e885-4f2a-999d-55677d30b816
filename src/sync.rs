use crate::database::{Database, get_setting, set_setting};
use sqlx::{SqlitePool, Row};
use crate::models::*;
use anyhow::Result;
use chrono::Utc;
use log::{info, error, warn};
use reqwest::Client;
use serde_json;
use std::time::Duration;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use tokio::time::sleep;
use uuid::Uuid;

const SYNC_INTERVAL_SECONDS: u64 = 30;
const SYNC_SERVER_URL: &str = "http://localhost:3001/api/sync"; // Configure this for your server

pub struct SyncManager {
    client: Client,
    till_id: String,
}

impl SyncManager {
    pub fn new(till_id: String) -> Self {
        Self {
            client: Client::new(),
            till_id,
        }
    }
    
    pub async fn sync_data(&self, db: &Database) -> Result<SyncStatus> {
        let mut is_online = false;
        let mut pending_changes = 0;
        
        // Check if we can reach the sync server
        match self.check_connectivity().await {
            Ok(_) => {
                is_online = true;
                info!("Sync server is reachable");
                
                // Perform sync operations
                match self.perform_sync(db).await {
                    Ok(synced_count) => {
                        info!("Successfully synced {} records", synced_count);
                        set_setting(db, "last_sync", &Utc::now().to_rfc3339()).await?;
                    }
                    Err(e) => {
                        error!("Sync failed: {}", e);
                        is_online = false;
                    }
                }
            }
            Err(e) => {
                warn!("Sync server unreachable: {}", e);
                is_online = false;
            }
        }
        
        // Count pending changes
        pending_changes = self.count_pending_changes(db).await?;
        
        let last_sync = get_setting(db, "last_sync").await?
            .and_then(|s| s.parse().ok());
        
        Ok(SyncStatus {
            is_online,
            last_sync,
            pending_changes,
            till_id: self.till_id.clone(),
        })
    }
    
    async fn check_connectivity(&self) -> Result<()> {
        let response = self.client
            .get(&format!("{}/health", SYNC_SERVER_URL))
            .timeout(Duration::from_secs(5))
            .send()
            .await?;
        
        if response.status().is_success() {
            Ok(())
        } else {
            Err(anyhow::anyhow!("Server returned error status: {}", response.status()))
        }
    }
    
    async fn perform_sync(&self, db: &Database) -> Result<i32> {
        let mut synced_count = 0;
        
        // Get unsynced records
        let unsynced_logs = self.get_unsynced_logs(db).await?;
        
        if unsynced_logs.is_empty() {
            info!("No records to sync");
            return Ok(0);
        }
        
        // Send data to server
        let sync_payload = SyncPayload {
            till_id: self.till_id.clone(),
            logs: unsynced_logs.clone(),
        };
        
        let response = self.client
            .post(&format!("{}/upload", SYNC_SERVER_URL))
            .json(&sync_payload)
            .timeout(Duration::from_secs(30))
            .send()
            .await?;
        
        if response.status().is_success() {
            // Mark records as synced
            for log in &unsynced_logs {
                self.mark_log_as_synced(db, &log.id).await?;
                synced_count += 1;
            }
        } else {
            return Err(anyhow::anyhow!("Failed to upload data: {}", response.status()));
        }
        
        // Download updates from other tills
        let last_sync = get_setting(db, "last_sync").await?
            .unwrap_or_else(|| "1970-01-01T00:00:00Z".to_string());
        
        let download_response = self.client
            .get(&format!("{}/download", SYNC_SERVER_URL))
            .query(&[("till_id", &self.till_id), ("since", &last_sync)])
            .timeout(Duration::from_secs(30))
            .send()
            .await?;
        
        if download_response.status().is_success() {
            let remote_data: SyncDownloadResponse = download_response.json().await?;
            self.apply_remote_changes(db, remote_data.logs).await?;
        }
        
        Ok(synced_count)
    }
    
    async fn get_unsynced_logs(&self, db: &Database) -> Result<Vec<SyncLog>> {
        let rows = sqlx::query(
            "SELECT * FROM sync_logs WHERE is_synced = FALSE ORDER BY created_at ASC LIMIT 100"
        )
        .fetch_all(db)
        .await?;
        
        let mut logs = Vec::new();
        for row in rows {
            let created_at: String = sqlx::Row::get(&row, "created_at");
            logs.push(SyncLog {
                id: sqlx::Row::get(&row, "id"),
                table_name: sqlx::Row::get(&row, "table_name"),
                record_id: sqlx::Row::get(&row, "record_id"),
                operation: sqlx::Row::get(&row, "operation"),
                data: sqlx::Row::get(&row, "data"),
                till_id: sqlx::Row::get(&row, "till_id"),
                created_at: created_at.parse().unwrap_or(Utc::now()),
                is_synced: sqlx::Row::get(&row, "is_synced"),
            });
        }
        
        Ok(logs)
    }
    
    async fn mark_log_as_synced(&self, db: &Database, log_id: &str) -> Result<()> {
        sqlx::query("UPDATE sync_logs SET is_synced = TRUE WHERE id = ?")
            .bind(log_id)
            .execute(db)
            .await?;
        Ok(())
    }
    
    async fn count_pending_changes(&self, db: &Database) -> Result<i32> {
        let row = sqlx::query("SELECT COUNT(*) as count FROM sync_logs WHERE is_synced = FALSE")
            .fetch_one(db)
            .await?;
        Ok(sqlx::Row::get::<i32, _>(&row, "count"))
    }
    
    async fn apply_remote_changes(&self, db: &Database, logs: Vec<SyncLog>) -> Result<()> {
        for log in logs {
            // Skip changes from our own till
            if log.till_id == self.till_id {
                continue;
            }
            
            match log.table_name.as_str() {
                "products" => {
                    self.apply_product_change(db, &log).await?;
                }
                "sales" => {
                    self.apply_sale_change(db, &log).await?;
                }
                _ => {
                    warn!("Unknown table in sync log: {}", log.table_name);
                }
            }
        }
        Ok(())
    }
    
    async fn apply_product_change(&self, db: &Database, log: &SyncLog) -> Result<()> {
        match log.operation.as_str() {
            "INSERT" | "UPDATE" => {
                let product: Product = serde_json::from_str(&log.data)?;
                
                // Check if product exists
                let exists = sqlx::query("SELECT COUNT(*) as count FROM products WHERE id = ?")
                    .bind(&product.id)
                    .fetch_one(db)
                    .await?
                    .get::<i32, _>("count") > 0;
                
                if exists {
                    crate::database::update_product(db, &product).await?;
                } else {
                    crate::database::insert_product(db, &product).await?;
                }
            }
            "DELETE" => {
                crate::database::delete_product(db, &log.record_id, &log.till_id).await?;
            }
            _ => {
                warn!("Unknown operation in sync log: {}", log.operation);
            }
        }
        Ok(())
    }
    
    async fn apply_sale_change(&self, db: &Database, log: &SyncLog) -> Result<()> {
        match log.operation.as_str() {
            "INSERT" => {
                let sale: Sale = serde_json::from_str(&log.data)?;
                
                // Check if sale already exists
                let exists = sqlx::query("SELECT COUNT(*) as count FROM sales WHERE id = ?")
                    .bind(&sale.id)
                    .fetch_one(db)
                    .await?
                    .get::<i32, _>("count") > 0;
                
                if !exists {
                    crate::database::insert_sale(db, &sale).await?;
                }
            }
            _ => {
                warn!("Unsupported operation for sales: {}", log.operation);
            }
        }
        Ok(())
    }
}

pub async fn log_change(db: &Database, table_name: &str, record_id: &str, operation: &str, data: &str, till_id: &str) -> Result<()> {
    let log = SyncLog {
        id: Uuid::new_v4().to_string(),
        table_name: table_name.to_string(),
        record_id: record_id.to_string(),
        operation: operation.to_string(),
        data: data.to_string(),
        till_id: till_id.to_string(),
        created_at: Utc::now(),
        is_synced: false,
    };
    
    sqlx::query(
        r#"
        INSERT INTO sync_logs (id, table_name, record_id, operation, data, till_id, created_at, is_synced)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        "#,
    )
    .bind(&log.id)
    .bind(&log.table_name)
    .bind(&log.record_id)
    .bind(&log.operation)
    .bind(&log.data)
    .bind(&log.till_id)
    .bind(log.created_at.to_rfc3339())
    .bind(log.is_synced)
    .execute(db)
    .await?;
    
    Ok(())
}

pub async fn start_background_sync(app_handle: AppHandle) {
    info!("Starting background sync task");
    
    loop {
        sleep(Duration::from_secs(SYNC_INTERVAL_SECONDS)).await;
        
        let db_state = app_handle.state::<std::sync::Arc<tokio::sync::Mutex<Database>>>();
        let db = db_state.lock().await;
        
        // Get till ID from settings
        let till_id = match get_setting(&*db, "till_id").await {
            Ok(Some(id)) => id,
            _ => {
                warn!("Till ID not set, skipping sync");
                continue;
            }
        };
        
        let sync_manager = SyncManager::new(till_id);
        
        match sync_manager.sync_data(&*db).await {
            Ok(status) => {
                // Emit sync status to frontend
                app_handle.emit_all("sync-status", &status).unwrap_or_else(|e| {
                    error!("Failed to emit sync status: {}", e);
                });
            }
            Err(e) => {
                error!("Background sync failed: {}", e);
            }
        }
    }
}

#[derive(serde::Serialize, serde::Deserialize)]
struct SyncPayload {
    till_id: String,
    logs: Vec<SyncLog>,
}

#[derive(serde::Serialize, serde::Deserialize)]
struct SyncDownloadResponse {
    logs: Vec<SyncLog>,
}