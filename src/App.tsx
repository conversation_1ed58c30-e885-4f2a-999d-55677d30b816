import { Routes, Route } from "react-router-dom";
import { useEffect } from "react";
import { listen } from "@tauri-apps/api/event";
import toast from "react-hot-toast";
import Layout from "./components/Layout";
import Dashboard from "./pages/Dashboard";
import Products from "./pages/Products";
import Sales from "./pages/Sales";
import POS from "./pages/POS";
import Settings from "./pages/Settings";
import { usePOSStore } from "./store/posStore";
import { SyncStatus } from "./types";

function App() {
  const { setSyncStatus } = usePOSStore();

  useEffect(() => {
    // Listen for sync status updates from the backend
    const unlisten = listen<SyncStatus>('sync-status', (event) => {
      setSyncStatus(event.payload);
      
      // Show toast notifications for sync status changes
      if (event.payload.is_online) {
        toast.success('Connected to sync server');
      } else if (event.payload.pending_changes > 0) {
        toast(`${event.payload.pending_changes} changes pending sync`, { icon: '⚠️' });
      }
    });

    return () => {
      unlisten.then(fn => fn());
    };
  }, [setSyncStatus]);

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/pos" element={<POS />} />
        <Route path="/products" element={<Products />} />
        <Route path="/sales" element={<Sales />} />
        <Route path="/settings" element={<Settings />} />
      </Routes>
    </Layout>
  );
}

export default App;