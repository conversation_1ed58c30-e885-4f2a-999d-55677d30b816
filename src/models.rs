use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Product {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub price: f64,
    pub stock_quantity: i32,
    pub barcode: Option<String>,
    pub category: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_deleted: bool,
    pub last_modified_by: String, // till_id
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Sale {
    pub id: String,
    pub total_amount: f64,
    pub tax_amount: f64,
    pub discount_amount: f64,
    pub payment_method: String,
    pub customer_id: Option<String>,
    pub till_id: String,
    pub created_at: DateTime<Utc>,
    pub is_synced: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct SaleItem {
    pub id: String,
    pub sale_id: String,
    pub product_id: String,
    pub quantity: i32,
    pub unit_price: f64,
    pub total_price: f64,
    pub discount_amount: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct SyncLog {
    pub id: String,
    pub table_name: String,
    pub record_id: String,
    pub operation: String, // INSERT, UPDATE, DELETE
    pub data: String, // JSON data
    pub till_id: String,
    pub created_at: DateTime<Utc>,
    pub is_synced: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateProductRequest {
    pub name: String,
    pub description: Option<String>,
    pub price: f64,
    pub stock_quantity: i32,
    pub barcode: Option<String>,
    pub category: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateProductRequest {
    pub id: String,
    pub name: Option<String>,
    pub description: Option<String>,
    pub price: Option<f64>,
    pub stock_quantity: Option<i32>,
    pub barcode: Option<String>,
    pub category: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSaleRequest {
    pub items: Vec<SaleItemRequest>,
    pub payment_method: String,
    pub customer_id: Option<String>,
    pub discount_amount: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SaleItemRequest {
    pub product_id: String,
    pub quantity: i32,
    pub discount_amount: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncStatus {
    pub is_online: bool,
    pub last_sync: Option<DateTime<Utc>>,
    pub pending_changes: i32,
    pub till_id: String,
}

impl Product {
    pub fn new(request: CreateProductRequest, till_id: String) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            name: request.name,
            description: request.description,
            price: request.price,
            stock_quantity: request.stock_quantity,
            barcode: request.barcode,
            category: request.category,
            created_at: now,
            updated_at: now,
            is_deleted: false,
            last_modified_by: till_id,
        }
    }
}

impl Sale {
    pub fn new(total_amount: f64, tax_amount: f64, discount_amount: f64, payment_method: String, customer_id: Option<String>, till_id: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            total_amount,
            tax_amount,
            discount_amount,
            payment_method,
            customer_id,
            till_id,
            created_at: Utc::now(),
            is_synced: false,
        }
    }
}

impl SaleItem {
    pub fn new(sale_id: String, product_id: String, quantity: i32, unit_price: f64, discount_amount: f64) -> Self {
        let total_price = (unit_price * quantity as f64) - discount_amount;
        Self {
            id: Uuid::new_v4().to_string(),
            sale_id,
            product_id,
            quantity,
            unit_price,
            total_price,
            discount_amount,
        }
    }
}