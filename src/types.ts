export interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  stock_quantity: number;
  barcode?: string;
  category?: string;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  last_modified_by: string;
  // Optional fields that might be used in frontend
  cost?: number;
  min_stock_level?: number;
}

export interface CreateProductRequest {
  name: string;
  description?: string;
  price: number;
  stock_quantity: number;
  barcode?: string;
  category?: string;
  cost?: number;
}

export interface UpdateProductRequest {
  id: string;
  name?: string;
  description?: string;
  price?: number;
  stock_quantity?: number;
  barcode?: string;
  category?: string;
  cost?: number;
}

export interface Sale {
  id: string;
  total_amount: number;
  tax_amount: number;
  discount_amount: number;
  payment_method: string;
  customer_id?: string;
  till_id: string;
  created_at: string;
  is_synced: boolean;
  // Computed field for frontend
  subtotal_amount?: number;
}

export interface SaleItem {
  id: string;
  sale_id: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  discount_amount: number;
  // Optional field for frontend display
  product_name?: string;
}

export interface CreateSaleRequest {
  items: SaleItemRequest[];
  payment_method: string;
  customer_id?: string;
  discount_amount?: number;
}

export interface SaleItemRequest {
  product_id: string;
  quantity: number;
  discount_amount?: number;
}

export interface SyncStatus {
  is_online: boolean;
  last_sync?: string;
  pending_changes: number;
  till_id: string;
  // Additional frontend fields
  isConnected?: boolean;
  lastSync?: string;
  pendingChanges?: number;
  isSyncing?: boolean;
}

export interface CartItem {
  product: Product;
  quantity: number;
  discount_amount: number;
}

export interface DashboardStats {
  todaySales: number;
  totalProducts: number;
  lowStockProducts: number;
  pendingSync: number;
}

export type PaymentMethod = 'cash' | 'card' | 'mobile';

export interface POSState {
  cart: CartItem[];
  selectedPaymentMethod: PaymentMethod;
  customerDiscount: number;
  isProcessing: boolean;
}

export interface POSStore extends POSState {
  syncStatus: SyncStatus | null;
  performSync: () => Promise<void>;
}