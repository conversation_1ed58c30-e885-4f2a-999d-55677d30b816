// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod database;
mod models;
mod sync;
mod commands;

use std::sync::Arc;
use tokio::sync::Mutex;

#[tokio::main]
async fn main() {
    env_logger::init();
    
    // Initialize database
    let db = database::init_database().await.expect("Failed to initialize database");
    let db_state = Arc::new(Mutex::new(db));
    
    tauri::Builder::default()
        .manage(db_state)
        .invoke_handler(tauri::generate_handler![
            commands::add_product,
            commands::get_products,
            commands::update_product,
            commands::delete_product,
            commands::create_sale,
            commands::get_sales,
            commands::get_sale_items,
            commands::sync_data,
            commands::get_sync_status,
            commands::set_till_id
        ])
        .setup(|app| {
            // Start background sync task
            let app_handle = app.handle();
            tokio::spawn(async move {
                sync::start_background_sync(app_handle).await;
            });
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}