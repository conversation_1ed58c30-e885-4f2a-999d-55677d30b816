import { useState, useEffect } from 'react';
import {
  MagnifyingGlassIcon,
  CalendarIcon,
  EyeIcon,
  DocumentTextIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { usePOSStore } from '../store/posStore';
import { Sale, SaleItem } from '../types';
import { format, startOfDay, endOfDay, subDays, isWithinInterval } from 'date-fns';
import clsx from 'clsx';

type DateFilter = 'today' | 'yesterday' | 'last7days' | 'last30days' | 'custom';

export default function Sales() {
  const { sales, loadSales } = usePOSStore();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState<DateFilter>('today');
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  const [saleItems, setSaleItems] = useState<SaleItem[]>([]);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    loadSales();
  }, [loadSales]);

  // Filter sales based on date and search
  const filteredSales = sales.filter(sale => {
    // Date filtering
    const saleDate = new Date(sale.created_at);
    let dateMatch = true;
    
    switch (dateFilter) {
      case 'today':
        dateMatch = isWithinInterval(saleDate, {
          start: startOfDay(new Date()),
          end: endOfDay(new Date())
        });
        break;
      case 'yesterday':
        const yesterday = subDays(new Date(), 1);
        dateMatch = isWithinInterval(saleDate, {
          start: startOfDay(yesterday),
          end: endOfDay(yesterday)
        });
        break;
      case 'last7days':
        dateMatch = isWithinInterval(saleDate, {
          start: startOfDay(subDays(new Date(), 7)),
          end: endOfDay(new Date())
        });
        break;
      case 'last30days':
        dateMatch = isWithinInterval(saleDate, {
          start: startOfDay(subDays(new Date(), 30)),
          end: endOfDay(new Date())
        });
        break;
      case 'custom':
        if (customStartDate && customEndDate) {
          dateMatch = isWithinInterval(saleDate, {
            start: startOfDay(new Date(customStartDate)),
            end: endOfDay(new Date(customEndDate))
          });
        }
        break;
    }
    
    // Search filtering
    const searchMatch = !searchTerm || 
      sale.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sale.payment_method.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sale.total_amount.toString().includes(searchTerm);
    
    return dateMatch && searchMatch;
  });

  // Calculate statistics
  const totalSales = filteredSales.reduce((sum, sale) => sum + sale.total_amount, 0);
  const totalTransactions = filteredSales.length;
  const averageTransaction = totalTransactions > 0 ? totalSales / totalTransactions : 0;
  
  // Payment method breakdown
  const paymentMethodStats = filteredSales.reduce((acc, sale) => {
    acc[sale.payment_method] = (acc[sale.payment_method] || 0) + sale.total_amount;
    return acc;
  }, {} as Record<string, number>);

  const handleViewDetails = async (sale: Sale) => {
    setSelectedSale(sale);
    try {
      // In a real app, you'd fetch sale items from the backend
      // For now, we'll simulate this
      setSaleItems([]);
      setShowDetails(true);
    } catch (error) {
      console.error('Error loading sale details:', error);
    }
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedSale(null);
    setSaleItems([]);
  };

  const getPaymentMethodColor = (method: string) => {
    switch (method.toLowerCase()) {
      case 'cash':
        return 'text-success-600 bg-success-100';
      case 'card':
        return 'text-primary-600 bg-primary-100';
      case 'mobile':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Sales</h1>
          <p className="text-gray-600">View and analyze your sales data</p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="card-content p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-8 w-8 text-success-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Sales</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${totalSales.toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-content p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentTextIcon className="h-8 w-8 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Transactions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {totalTransactions}
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-content p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarIcon className="h-8 w-8 text-warning-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Average Sale</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${averageTransaction.toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-content p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-600">Payment Methods</p>
              {Object.entries(paymentMethodStats).map(([method, amount]) => (
                <div key={method} className="flex justify-between text-sm">
                  <span className="capitalize">{method}:</span>
                  <span className="font-medium">${amount.toFixed(2)}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-content p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search sales..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10 w-full"
              />
            </div>
            
            {/* Date Filter */}
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value as DateFilter)}
              className="input"
            >
              <option value="today">Today</option>
              <option value="yesterday">Yesterday</option>
              <option value="last7days">Last 7 Days</option>
              <option value="last30days">Last 30 Days</option>
              <option value="custom">Custom Range</option>
            </select>
            
            {/* Custom Date Range */}
            {dateFilter === 'custom' && (
              <>
                <input
                  type="date"
                  value={customStartDate}
                  onChange={(e) => setCustomStartDate(e.target.value)}
                  className="input"
                  placeholder="Start Date"
                />
                <input
                  type="date"
                  value={customEndDate}
                  onChange={(e) => setCustomEndDate(e.target.value)}
                  className="input"
                  placeholder="End Date"
                />
              </>
            )}
          </div>
        </div>
      </div>

      {/* Sales Table */}
      <div className="card">
        <div className="card-content p-0">
          <div className="overflow-x-auto">
            <table className="table">
              <thead>
                <tr>
                  <th>Sale ID</th>
                  <th>Date & Time</th>
                  <th>Payment Method</th>
                  <th>Subtotal</th>
                  <th>Discount</th>
                  <th>Tax</th>
                  <th>Total</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredSales.map((sale) => (
                  <tr key={sale.id}>
                    <td className="font-mono text-sm">
                      {sale.id.slice(0, 8)}...
                    </td>
                    <td>
                      <div>
                        <div className="font-medium">
                          {format(new Date(sale.created_at), 'MMM dd, yyyy')}
                        </div>
                        <div className="text-sm text-gray-500">
                          {format(new Date(sale.created_at), 'HH:mm:ss')}
                        </div>
                      </div>
                    </td>
                    <td>
                      <span className={clsx(
                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize',
                        getPaymentMethodColor(sale.payment_method)
                      )}>
                        {sale.payment_method}
                      </span>
                    </td>
                    <td className="font-medium">
                      ${sale.subtotal_amount.toFixed(2)}
                    </td>
                    <td className="text-success-600">
                      {sale.discount_amount > 0 ? `-$${sale.discount_amount.toFixed(2)}` : '-'}
                    </td>
                    <td className="font-medium">
                      ${sale.tax_amount.toFixed(2)}
                    </td>
                    <td className="font-bold text-lg">
                      ${sale.total_amount.toFixed(2)}
                    </td>
                    <td>
                      <button
                        onClick={() => handleViewDetails(sale)}
                        className="btn-sm btn-secondary"
                        title="View details"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {filteredSales.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500">No sales found for the selected period</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Sale Details Modal */}
      {showDetails && selectedSale && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">
                  Sale Details
                </h2>
                <button
                  onClick={handleCloseDetails}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>
            
            <div className="px-6 py-4 space-y-6">
              {/* Sale Information */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Sale ID</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono">{selectedSale.id}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Date & Time</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {format(new Date(selectedSale.created_at), 'MMM dd, yyyy HH:mm:ss')}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Payment Method</label>
                  <p className="mt-1">
                    <span className={clsx(
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize',
                      getPaymentMethodColor(selectedSale.payment_method)
                    )}>
                      {selectedSale.payment_method}
                    </span>
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Till ID</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedSale.till_id || 'N/A'}</p>
                </div>
              </div>
              
              {/* Sale Items */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Items</h3>
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Product
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Qty
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Discount
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {saleItems.map((item) => (
                        <tr key={item.id}>
                          <td className="px-4 py-3 text-sm text-gray-900">
                            {item.product_name}
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-900">
                            ${item.unit_price.toFixed(2)}
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-900">
                            {item.quantity}
                          </td>
                          <td className="px-4 py-3 text-sm text-success-600">
                            {item.discount_amount > 0 ? `-$${item.discount_amount.toFixed(2)}` : '-'}
                          </td>
                          <td className="px-4 py-3 text-sm font-medium text-gray-900">
                            ${item.total_price.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              
              {/* Sale Totals */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal:</span>
                    <span>${selectedSale.subtotal_amount.toFixed(2)}</span>
                  </div>
                  {selectedSale.discount_amount > 0 && (
                    <div className="flex justify-between text-sm text-success-600">
                      <span>Discount:</span>
                      <span>-${selectedSale.discount_amount.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="flex justify-between text-sm">
                    <span>Tax:</span>
                    <span>${selectedSale.tax_amount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                    <span>Total:</span>
                    <span>${selectedSale.total_amount.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end">
              <button
                onClick={handleCloseDetails}
                className="btn-secondary"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}