import { useEffect, useState } from 'react';
import {
  CurrencyDollarIcon,
  CubeIcon,
  ExclamationTriangleIcon,
  CloudArrowUpIcon,
} from '@heroicons/react/24/outline';
import { usePOSStore } from '../store/posStore';
import { DashboardStats } from '../types';
import { format } from 'date-fns';

export default function Dashboard() {
  const { products, sales, syncStatus, loadSales } = usePOSStore();
  const [stats, setStats] = useState<DashboardStats>({
    todaySales: 0,
    totalProducts: 0,
    lowStockProducts: 0,
    pendingSync: 0,
  });

  useEffect(() => {
    // Load recent sales for today's statistics
    loadSales(50);
  }, [loadSales]);

  useEffect(() => {
    // Calculate dashboard statistics
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todaySales = sales
      .filter(sale => new Date(sale.created_at) >= today)
      .reduce((sum, sale) => sum + sale.total_amount, 0);
    
    const totalProducts = products.length;
    const lowStockProducts = products.filter(product => product.stock_quantity <= 5).length;
    const pendingSync = syncStatus?.pending_changes || 0;
    
    setStats({
      todaySales,
      totalProducts,
      lowStockProducts,
      pendingSync,
    });
  }, [products, sales, syncStatus]);

  const statCards = [
    {
      name: "Today's Sales",
      value: `$${stats.todaySales.toFixed(2)}`,
      icon: CurrencyDollarIcon,
      color: 'text-success-600',
      bgColor: 'bg-success-50',
    },
    {
      name: 'Total Products',
      value: stats.totalProducts.toString(),
      icon: CubeIcon,
      color: 'text-primary-600',
      bgColor: 'bg-primary-50',
    },
    {
      name: 'Low Stock Items',
      value: stats.lowStockProducts.toString(),
      icon: ExclamationTriangleIcon,
      color: 'text-warning-600',
      bgColor: 'bg-warning-50',
    },
    {
      name: 'Pending Sync',
      value: stats.pendingSync.toString(),
      icon: CloudArrowUpIcon,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
    },
  ];

  const recentSales = sales.slice(0, 5);
  const lowStockProducts = products.filter(product => product.stock_quantity <= 5).slice(0, 5);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-2 text-gray-600">
          Welcome back! Here's what's happening with your store today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className={`rounded-lg p-3 ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Recent Sales */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Recent Sales</h3>
            <p className="card-description">Latest transactions from your store</p>
          </div>
          <div className="card-content">
            {recentSales.length > 0 ? (
              <div className="space-y-4">
                {recentSales.map((sale) => (
                  <div key={sale.id} className="flex items-center justify-between border-b border-gray-100 pb-4 last:border-0">
                    <div>
                      <p className="font-medium text-gray-900">
                        Sale #{sale.id.slice(-8)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {format(new Date(sale.created_at), 'MMM dd, yyyy HH:mm')}
                      </p>
                      <p className="text-sm text-gray-500">
                        Payment: {sale.payment_method}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">
                        ${sale.total_amount.toFixed(2)}
                      </p>
                      <div className="flex items-center space-x-2">
                        <span className={`badge ${
                          sale.is_synced ? 'badge-success' : 'badge-warning'
                        }`}>
                          {sale.is_synced ? 'Synced' : 'Pending'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center text-gray-500 py-8">
                No sales recorded yet
              </p>
            )}
          </div>
        </div>

        {/* Low Stock Alert */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Low Stock Alert</h3>
            <p className="card-description">Products running low on inventory</p>
          </div>
          <div className="card-content">
            {lowStockProducts.length > 0 ? (
              <div className="space-y-4">
                {lowStockProducts.map((product) => (
                  <div key={product.id} className="flex items-center justify-between border-b border-gray-100 pb-4 last:border-0">
                    <div>
                      <p className="font-medium text-gray-900">{product.name}</p>
                      <p className="text-sm text-gray-500">
                        ${product.price.toFixed(2)}
                      </p>
                      {product.category && (
                        <span className="badge badge-secondary text-xs">
                          {product.category}
                        </span>
                      )}
                    </div>
                    <div className="text-right">
                      <p className={`font-semibold ${
                        product.stock_quantity === 0 
                          ? 'text-error-600' 
                          : product.stock_quantity <= 2 
                          ? 'text-warning-600' 
                          : 'text-gray-900'
                      }`}>
                        {product.stock_quantity} left
                      </p>
                      <p className="text-sm text-gray-500">
                        {product.stock_quantity === 0 ? 'Out of stock' : 'Low stock'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center text-gray-500 py-8">
                All products are well stocked!
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Sync Status */}
      {syncStatus && (
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Sync Status</h3>
            <p className="card-description">Data synchronization information</p>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <p className="text-sm font-medium text-gray-600">Connection Status</p>
                <div className="flex items-center space-x-2 mt-1">
                  <div className={`h-2 w-2 rounded-full ${
                    syncStatus.is_online ? 'bg-success-500' : 'bg-warning-500'
                  }`} />
                  <span className="text-sm text-gray-900">
                    {syncStatus.is_online ? 'Online' : 'Offline'}
                  </span>
                </div>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-600">Till ID</p>
                <p className="text-sm text-gray-900 mt-1">{syncStatus.till_id}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-600">Last Sync</p>
                <p className="text-sm text-gray-900 mt-1">
                  {syncStatus.last_sync 
                    ? format(new Date(syncStatus.last_sync), 'MMM dd, HH:mm')
                    : 'Never'
                  }
                </p>
              </div>
            </div>
            
            {syncStatus.pending_changes > 0 && (
              <div className="mt-4 p-3 bg-warning-50 border border-warning-200 rounded-md">
                <p className="text-sm text-warning-800">
                  <ExclamationTriangleIcon className="h-4 w-4 inline mr-1" />
                  {syncStatus.pending_changes} changes are pending synchronization
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}