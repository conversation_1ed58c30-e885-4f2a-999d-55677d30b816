import { useState, useEffect } from 'react';
import {
  CogIcon,
  ComputerDesktopIcon,
  CloudIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { usePOSStore } from '../store/posStore';
import { invoke } from '@tauri-apps/api/tauri';
import toast from 'react-hot-toast';
import clsx from 'clsx';

interface TillSettings {
  till_id: string;
  till_name: string;
  server_url: string;
  sync_interval: number;
  auto_sync: boolean;
  receipt_footer: string;
  tax_rate: number;
}

const defaultSettings: TillSettings = {
  till_id: '',
  till_name: '',
  server_url: '',
  sync_interval: 300, // 5 minutes
  auto_sync: true,
  receipt_footer: 'Thank you for your business!',
  tax_rate: 0.1, // 10%
};

export default function Settings() {
  const { syncStatus, performSync } = usePOSStore();
  
  const [settings, setSettings] = useState<TillSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'general' | 'sync' | 'receipt' | 'advanced'>('general');
  const [testConnection, setTestConnection] = useState<'idle' | 'testing' | 'success' | 'error'>('idle');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const loadedSettings = await invoke<TillSettings>('get_till_settings');
      setSettings({ ...defaultSettings, ...loadedSettings });
    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setIsSaving(true);
      await invoke('set_till_settings', { settings });
      toast.success('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestConnection = async () => {
    if (!settings.server_url) {
      toast.error('Please enter a server URL first');
      return;
    }

    try {
      setTestConnection('testing');
      // Test connection to server
      const response = await fetch(`${settings.server_url}/health`, {
        method: 'GET',
        timeout: 5000,
      });
      
      if (response.ok) {
        setTestConnection('success');
        toast.success('Connection successful!');
      } else {
        setTestConnection('error');
        toast.error('Connection failed: Server responded with error');
      }
    } catch (error) {
      setTestConnection('error');
      toast.error('Connection failed: Unable to reach server');
    }

    // Reset test status after 3 seconds
    setTimeout(() => setTestConnection('idle'), 3000);
  };

  const handleManualSync = async () => {
    try {
      await performSync();
      toast.success('Manual sync completed');
    } catch (error) {
      console.error('Manual sync failed:', error);
      toast.error('Manual sync failed');
    }
  };

  const handleInputChange = (field: keyof TillSettings, value: string | number | boolean) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const generateTillId = () => {
    const id = `TILL-${Date.now().toString(36).toUpperCase()}`;
    handleInputChange('till_id', id);
  };

  const tabs = [
    { id: 'general', name: 'General', icon: CogIcon },
    { id: 'sync', name: 'Synchronization', icon: CloudIcon },
    { id: 'receipt', name: 'Receipt', icon: DocumentTextIcon },
    { id: 'advanced', name: 'Advanced', icon: ShieldCheckIcon },
  ] as const;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Configure your POS system</p>
        </div>
        <button
          onClick={saveSettings}
          disabled={isSaving}
          className="btn-primary"
        >
          {isSaving ? 'Saving...' : 'Save Settings'}
        </button>
      </div>

      {/* Sync Status Card */}
      <div className="card">
        <div className="card-content p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className={clsx(
                'flex items-center justify-center w-12 h-12 rounded-full',
                syncStatus.isConnected ? 'bg-success-100' : 'bg-error-100'
              )}>
                <CloudIcon className={clsx(
                  'h-6 w-6',
                  syncStatus.isConnected ? 'text-success-600' : 'text-error-600'
                )} />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  {syncStatus.isConnected ? 'Connected' : 'Disconnected'}
                </h3>
                <p className="text-sm text-gray-600">
                  {syncStatus.lastSync 
                    ? `Last sync: ${new Date(syncStatus.lastSync).toLocaleString()}`
                    : 'Never synced'
                  }
                </p>
                {syncStatus.pendingChanges > 0 && (
                  <p className="text-sm text-warning-600">
                    {syncStatus.pendingChanges} pending changes
                  </p>
                )}
              </div>
            </div>
            <button
              onClick={handleManualSync}
              disabled={syncStatus.isSyncing}
              className="btn-secondary"
            >
              {syncStatus.isSyncing ? 'Syncing...' : 'Sync Now'}
            </button>
          </div>
        </div>
      </div>

      {/* Settings Tabs */}
      <div className="card">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={clsx(
                    'flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm',
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )}
                >
                  <Icon className="h-5 w-5" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="card-content p-6">
          {/* General Tab */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Till ID
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={settings.till_id}
                      onChange={(e) => handleInputChange('till_id', e.target.value)}
                      className="input flex-1"
                      placeholder="Enter till ID"
                    />
                    <button
                      onClick={generateTillId}
                      className="btn-secondary whitespace-nowrap"
                    >
                      Generate
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Unique identifier for this till machine
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Till Name
                  </label>
                  <input
                    type="text"
                    value={settings.till_name}
                    onChange={(e) => handleInputChange('till_name', e.target.value)}
                    className="input w-full"
                    placeholder="Enter till name (e.g., Main Counter)"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Friendly name for this till
                  </p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tax Rate (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={settings.tax_rate * 100}
                  onChange={(e) => handleInputChange('tax_rate', parseFloat(e.target.value) / 100 || 0)}
                  className="input w-full md:w-48"
                  placeholder="10.00"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Default tax rate applied to sales
                </p>
              </div>
            </div>
          )}

          {/* Sync Tab */}
          {activeTab === 'sync' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Server URL
                </label>
                <div className="flex space-x-2">
                  <input
                    type="url"
                    value={settings.server_url}
                    onChange={(e) => handleInputChange('server_url', e.target.value)}
                    className="input flex-1"
                    placeholder="https://your-server.com/api"
                  />
                  <button
                    onClick={handleTestConnection}
                    disabled={testConnection === 'testing'}
                    className={clsx(
                      'btn-secondary whitespace-nowrap',
                      testConnection === 'success' && 'btn-success',
                      testConnection === 'error' && 'btn-error'
                    )}
                  >
                    {testConnection === 'testing' && 'Testing...'}
                    {testConnection === 'success' && 'Connected'}
                    {testConnection === 'error' && 'Failed'}
                    {testConnection === 'idle' && 'Test Connection'}
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  URL of the central server for data synchronization
                </p>
              </div>
              
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="auto_sync"
                  checked={settings.auto_sync}
                  onChange={(e) => handleInputChange('auto_sync', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="auto_sync" className="text-sm font-medium text-gray-700">
                  Enable automatic synchronization
                </label>
              </div>
              
              {settings.auto_sync && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sync Interval (seconds)
                  </label>
                  <select
                    value={settings.sync_interval}
                    onChange={(e) => handleInputChange('sync_interval', parseInt(e.target.value))}
                    className="input w-full md:w-48"
                  >
                    <option value={60}>1 minute</option>
                    <option value={300}>5 minutes</option>
                    <option value={600}>10 minutes</option>
                    <option value={1800}>30 minutes</option>
                    <option value={3600}>1 hour</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    How often to automatically sync with the server
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Receipt Tab */}
          {activeTab === 'receipt' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Receipt Footer
                </label>
                <textarea
                  value={settings.receipt_footer}
                  onChange={(e) => handleInputChange('receipt_footer', e.target.value)}
                  className="input w-full h-24 resize-none"
                  placeholder="Thank you for your business!"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Text displayed at the bottom of receipts
                </p>
              </div>
              
              {/* Receipt Preview */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Receipt Preview
                </label>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 font-mono text-sm">
                  <div className="text-center border-b border-gray-300 pb-2 mb-2">
                    <div className="font-bold">{settings.till_name || 'Your Store'}</div>
                    <div className="text-xs text-gray-600">Receipt #{new Date().getTime()}</div>
                    <div className="text-xs text-gray-600">{new Date().toLocaleString()}</div>
                  </div>
                  
                  <div className="space-y-1 mb-2">
                    <div className="flex justify-between">
                      <span>Sample Product</span>
                      <span>$10.00</span>
                    </div>
                    <div className="flex justify-between text-xs text-gray-600">
                      <span>  1 x $10.00</span>
                      <span></span>
                    </div>
                  </div>
                  
                  <div className="border-t border-gray-300 pt-2 space-y-1">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>$10.00</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax ({(settings.tax_rate * 100).toFixed(1)}%):</span>
                      <span>${(10 * settings.tax_rate).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between font-bold border-t border-gray-300 pt-1">
                      <span>Total:</span>
                      <span>${(10 + 10 * settings.tax_rate).toFixed(2)}</span>
                    </div>
                  </div>
                  
                  {settings.receipt_footer && (
                    <div className="text-center text-xs text-gray-600 mt-4 pt-2 border-t border-gray-300">
                      {settings.receipt_footer}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Advanced Tab */}
          {activeTab === 'advanced' && (
            <div className="space-y-6">
              <div className="bg-warning-50 border border-warning-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <ExclamationTriangleIcon className="h-5 w-5 text-warning-600" />
                  <h3 className="font-medium text-warning-800">Advanced Settings</h3>
                </div>
                <p className="text-warning-700 text-sm">
                  These settings are for advanced users only. Changing them incorrectly may cause issues.
                </p>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-4">Database</h4>
                  <div className="space-y-3">
                    <button className="btn-secondary">
                      Export Database
                    </button>
                    <button className="btn-secondary">
                      Import Database
                    </button>
                    <button className="btn-error">
                      Reset Database
                    </button>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-4">Logs</h4>
                  <div className="space-y-3">
                    <button className="btn-secondary">
                      View Application Logs
                    </button>
                    <button className="btn-secondary">
                      View Sync Logs
                    </button>
                    <button className="btn-secondary">
                      Clear Logs
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}