import { useState } from 'react';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  MinusIcon,
  TrashIcon,
  CreditCardIcon,
  BanknotesIcon,
  DevicePhoneMobileIcon,
} from '@heroicons/react/24/outline';
import { usePOSStore } from '../store/posStore';
import { Product, PaymentMethod } from '../types';
import clsx from 'clsx';
import toast from 'react-hot-toast';

export default function POS() {
  const {
    products,
    posState,
    addToCart,
    removeFromCart,
    updateCartItemQuantity,
    updateCartItemDiscount,
    setPaymentMethod,
    setCustomerDiscount,
    processCheckout,
    clearCart,
  } = usePOSStore();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Filter products based on search and category
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.barcode?.includes(searchTerm) ||
                         product.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    
    return matchesSearch && matchesCategory && product.stock_quantity > 0;
  });

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(products.map(p => p.category).filter(Boolean)))];

  // Calculate totals
  const subtotal = posState.cart.reduce((sum, item) => {
    return sum + (item.product.price * item.quantity) - item.discount_amount;
  }, 0);
  
  const taxRate = 0.1; // 10% tax
  const discountAmount = posState.customerDiscount;
  const taxAmount = (subtotal - discountAmount) * taxRate;
  const total = subtotal - discountAmount + taxAmount;

  const paymentMethods: { id: PaymentMethod; name: string; icon: any }[] = [
    { id: 'cash', name: 'Cash', icon: BanknotesIcon },
    { id: 'card', name: 'Card', icon: CreditCardIcon },
    { id: 'mobile', name: 'Mobile', icon: DevicePhoneMobileIcon },
  ];

  const handleAddToCart = (product: Product) => {
    if (product.stock_quantity <= 0) {
      toast.error('Product is out of stock');
      return;
    }
    addToCart(product);
  };

  const handleCheckout = async () => {
    if (posState.cart.length === 0) {
      toast.error('Cart is empty');
      return;
    }
    
    // Check stock availability
    for (const item of posState.cart) {
      if (item.quantity > item.product.stock_quantity) {
        toast.error(`Not enough stock for ${item.product.name}`);
        return;
      }
    }
    
    await processCheckout();
  };

  return (
    <div className="h-screen flex">
      {/* Products Section */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-6">
          <h1 className="text-2xl font-bold text-gray-900">Point of Sale</h1>
          
          {/* Search and Filter */}
          <div className="mt-4 flex space-x-4">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search products by name, barcode, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10 w-full"
              />
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="input w-48"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        {/* Products Grid */}
        <div className="flex-1 overflow-auto p-6">
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                onClick={() => handleAddToCart(product)}
                className="card cursor-pointer hover:shadow-md transition-shadow"
              >
                <div className="card-content p-4">
                  <h3 className="font-medium text-gray-900 text-sm mb-1 line-clamp-2">
                    {product.name}
                  </h3>
                  <p className="text-lg font-bold text-primary-600 mb-2">
                    ${product.price.toFixed(2)}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Stock: {product.stock_quantity}</span>
                    {product.category && (
                      <span className="badge badge-secondary text-xs">
                        {product.category}
                      </span>
                    )}
                  </div>
                  {product.barcode && (
                    <p className="text-xs text-gray-400 mt-1 truncate">
                      {product.barcode}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          {filteredProducts.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No products found</p>
            </div>
          )}
        </div>
      </div>
      
      {/* Cart Section */}
      <div className="w-96 bg-white border-l border-gray-200 flex flex-col">
        {/* Cart Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Cart</h2>
            {posState.cart.length > 0 && (
              <button
                onClick={clearCart}
                className="btn-sm btn-secondary"
              >
                Clear All
              </button>
            )}
          </div>
        </div>
        
        {/* Cart Items */}
        <div className="flex-1 overflow-auto p-6">
          {posState.cart.length === 0 ? (
            <p className="text-center text-gray-500 py-8">
              Cart is empty
            </p>
          ) : (
            <div className="space-y-4">
              {posState.cart.map((item) => (
                <div key={item.product.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-gray-900 text-sm">
                      {item.product.name}
                    </h4>
                    <button
                      onClick={() => removeFromCart(item.product.id)}
                      className="text-gray-400 hover:text-error-500"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">
                      ${item.product.price.toFixed(2)} each
                    </span>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => updateCartItemQuantity(item.product.id, item.quantity - 1)}
                        className="btn-sm btn-secondary p-1"
                      >
                        <MinusIcon className="h-3 w-3" />
                      </button>
                      <span className="w-8 text-center text-sm font-medium">
                        {item.quantity}
                      </span>
                      <button
                        onClick={() => updateCartItemQuantity(item.product.id, item.quantity + 1)}
                        className="btn-sm btn-secondary p-1"
                        disabled={item.quantity >= item.product.stock_quantity}
                      >
                        <PlusIcon className="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                  
                  {/* Item Discount */}
                  <div className="flex items-center space-x-2 mb-2">
                    <label className="text-xs text-gray-600">Discount:</label>
                    <input
                      type="number"
                      min="0"
                      max={item.product.price * item.quantity}
                      step="0.01"
                      value={item.discount_amount}
                      onChange={(e) => updateCartItemDiscount(item.product.id, parseFloat(e.target.value) || 0)}
                      className="input text-xs h-6 w-20"
                    />
                  </div>
                  
                  <div className="text-right">
                    <span className="font-semibold text-gray-900">
                      ${((item.product.price * item.quantity) - item.discount_amount).toFixed(2)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Cart Summary and Checkout */}
        {posState.cart.length > 0 && (
          <div className="border-t border-gray-200 p-6">
            {/* Customer Discount */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Customer Discount
              </label>
              <input
                type="number"
                min="0"
                max={subtotal}
                step="0.01"
                value={posState.customerDiscount}
                onChange={(e) => setCustomerDiscount(parseFloat(e.target.value) || 0)}
                className="input w-full"
                placeholder="0.00"
              />
            </div>
            
            {/* Payment Method */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Payment Method
              </label>
              <div className="grid grid-cols-3 gap-2">
                {paymentMethods.map((method) => (
                  <button
                    key={method.id}
                    onClick={() => setPaymentMethod(method.id)}
                    className={clsx(
                      'btn-sm flex flex-col items-center space-y-1 p-3',
                      posState.selectedPaymentMethod === method.id
                        ? 'btn-primary'
                        : 'btn-secondary'
                    )}
                  >
                    <method.icon className="h-4 w-4" />
                    <span className="text-xs">{method.name}</span>
                  </button>
                ))}
              </div>
            </div>
            
            {/* Totals */}
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>
              {discountAmount > 0 && (
                <div className="flex justify-between text-sm text-success-600">
                  <span>Discount:</span>
                  <span>-${discountAmount.toFixed(2)}</span>
                </div>
              )}
              <div className="flex justify-between text-sm">
                <span>Tax (10%):</span>
                <span>${taxAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                <span>Total:</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div>
            
            {/* Checkout Button */}
            <button
              onClick={handleCheckout}
              disabled={posState.isProcessing || posState.cart.length === 0}
              className="btn-lg btn-success w-full"
            >
              {posState.isProcessing ? 'Processing...' : 'Complete Sale'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}