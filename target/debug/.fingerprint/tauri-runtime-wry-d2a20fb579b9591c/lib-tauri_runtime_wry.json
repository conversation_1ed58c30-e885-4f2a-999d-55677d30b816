{"rustc": 4723136837156968084, "features": "[\"arboard\", \"clipboard\", \"global-shortcut\", \"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 1633081050429914612, "deps": [[5788395569483560775, "arboard", false, 2322594260515417626], [8292277814562636972, "tauri_utils", false, 1793497724282020387], [8319709847752024821, "uuid", false, 10339700279421357674], [8391357152270261188, "wry", false, 8986660734652555382], [11693073011723388840, "raw_window_handle", false, 6731205830923669796], [13208667028893622512, "rand", false, 6900219625556240075], [14162324460024849578, "tauri_runtime", false, 17910456659691279875], [16228250612241359704, "build_script_build", false, 10073554806549431642], [16371049426708177877, "cocoa", false, 10017781449264370349]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-d2a20fb579b9591c/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}