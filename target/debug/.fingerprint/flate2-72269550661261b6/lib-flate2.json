{"rustc": 4723136837156968084, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 5347358027863023418, "path": 77767172520238610, "deps": [[4675849561795547236, "miniz_oxide", false, 4894206038802791660], [5466618496199522463, "crc32fast", false, 10373328614698369471]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-72269550661261b6/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}