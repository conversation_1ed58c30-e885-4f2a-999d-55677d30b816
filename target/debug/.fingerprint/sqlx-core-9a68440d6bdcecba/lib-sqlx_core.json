{"rustc": 4723136837156968084, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 5347358027863023418, "path": 1001496983934479202, "deps": [[5103565458935487, "futures_io", false, 8327948674027482686], [40386456601120721, "percent_encoding", false, 14090065963507666374], [530211389790465181, "hex", false, 1293253415979186787], [788558663644978524, "crossbeam_queue", false, 7366404696981612788], [966925859616469517, "ahash", false, 17798026255638301847], [1162433738665300155, "crc", false, 10274033669018537532], [1464803193346256239, "event_listener", false, 16226102844253651832], [1811549171721445101, "futures_channel", false, 5458160380965924605], [3129130049864710036, "memchr", false, 13226132519458249420], [3150220818285335163, "url", false, 1344501035966576598], [3405817021026194662, "hashlink", false, 4122060569866661558], [3646857438214563691, "futures_intrusive", false, 15806196364348530195], [3712811570531045576, "byteorder", false, 6738841056399747840], [3722963349756955755, "once_cell", false, 18347124574856344496], [5986029879202738730, "log", false, 11427039726786026724], [6048213226671835012, "smallvec", false, 7002496869064224227], [7620660491849607393, "futures_core", false, 12914485325312889039], [8008191657135824715, "thiserror", false, 7177988751108916753], [8319709847752024821, "uuid", false, 10339700279421357674], [8606274917505247608, "tracing", false, 13684006364517317893], [9538054652646069845, "tokio", false, 719054119088736892], [9689903380558560274, "serde", false, 2619042827247960912], [9857275760291862238, "sha2", false, 17296654753383165077], [9897246384292347999, "chrono", false, 3151649242798974561], [10629569228670356391, "futures_util", false, 10282654967338091349], [10862088793507253106, "sqlformat", false, 11661911232701146842], [11295624341523567602, "rustls", false, 6352072612442364279], [12170264697963848012, "either", false, 4722252644983722230], [14483812548788871374, "indexmap", false, 12377635823013668532], [15367738274754116744, "serde_json", false, 6364790342758265420], [16066129441945555748, "bytes", false, 3082263952613915852], [16311359161338405624, "rustls_pemfile", false, 7306481042436538574], [16973251432615581304, "tokio_stream", false, 8012038832657812852], [17106256174509013259, "atoi", false, 13485009952432812079], [17605717126308396068, "paste", false, 4556677029334308442], [17652733826348741533, "webpki_roots", false, 6126876092242958637]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-9a68440d6bdcecba/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}