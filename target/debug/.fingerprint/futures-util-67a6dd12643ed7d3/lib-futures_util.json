{"rustc": 4723136837156968084, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 336243669335521001, "path": 8304378074009131504, "deps": [[5103565458935487, "futures_io", false, 3142031048858743939], [1615478164327904835, "pin_utils", false, 7221875028065479722], [1906322745568073236, "pin_project_lite", false, 4816099685205994073], [3129130049864710036, "memchr", false, 4187728906282071879], [6955678925937229351, "slab", false, 9598773200490083750], [7013762810557009322, "futures_sink", false, 2004835039413723936], [7620660491849607393, "futures_core", false, 11064105602689656066], [10565019901765856648, "futures_macro", false, 15026622439567636478], [16240732885093539806, "futures_task", false, 13495105780381704663]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-67a6dd12643ed7d3/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}