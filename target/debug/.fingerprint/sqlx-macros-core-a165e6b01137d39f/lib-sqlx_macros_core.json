{"rustc": 4723136837156968084, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 3033921117576893, "path": 17687321624773381649, "deps": [[530211389790465181, "hex", false, 1293253415979186787], [996810380461694889, "sqlx_core", false, 15077168357729521117], [1441306149310335789, "tempfile", false, 8702473466658734981], [2713742371683562785, "syn", false, 11713761178691210934], [3060637413840920116, "proc_macro2", false, 3703143559497602325], [3150220818285335163, "url", false, 8352553727211378081], [3405707034081185165, "dotenvy", false, 6255810060318397768], [3722963349756955755, "once_cell", false, 18347124574856344496], [8045585743974080694, "heck", false, 17105938323618339979], [9538054652646069845, "tokio", false, 5363849649365812499], [9689903380558560274, "serde", false, 2619042827247960912], [9857275760291862238, "sha2", false, 17389771379438293884], [11838249260056359578, "sqlx_sqlite", false, 5428039518284378136], [12170264697963848012, "either", false, 4722252644983722230], [15367738274754116744, "serde_json", false, 16960842548497871022], [17990358020177143287, "quote", false, 6117099007456061250]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-core-a165e6b01137d39f/dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}