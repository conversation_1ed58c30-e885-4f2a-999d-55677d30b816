{"rustc": 4723136837156968084, "features": "[\"compression\", \"custom-protocol\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 3033921117576893, "path": 11813743724408116544, "deps": [[2713742371683562785, "syn", false, 11713761178691210934], [3060637413840920116, "proc_macro2", false, 3703143559497602325], [8292277814562636972, "tauri_utils", false, 13517178842853870141], [13077543566650298139, "heck", false, 12616656512282845906], [17492769205600034078, "tauri_codegen", false, 148205207992371334], [17990358020177143287, "quote", false, 6117099007456061250]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-288ae05e72bb8021/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}