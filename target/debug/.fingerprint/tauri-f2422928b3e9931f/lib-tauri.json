{"rustc": 4723136837156968084, "features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"bytes\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"custom-protocol\", \"default\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-request\", \"indexmap\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"tauri-runtime-wry\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 10450692478023715500, "path": 4799559525037460913, "deps": [[40386456601120721, "percent_encoding", false, 14090065963507666374], [500211409582349667, "shared_child", false, 13092277550646227263], [947818755262499932, "notify_rust", false, 10451392268664278265], [1260461579271933187, "serialize_to_javascript", false, 8110472943185908612], [1441306149310335789, "tempfile", false, 8702473466658734981], [3150220818285335163, "url", false, 1344501035966576598], [3722963349756955755, "once_cell", false, 18347124574856344496], [3988549704697787137, "open", false, 7672120010767715583], [4405182208873388884, "http", false, 17871174199404994561], [4450062412064442726, "dirs_next", false, 9695069096892915508], [4767930184903566869, "plist", false, 11140810052108993172], [4899080583175475170, "semver", false, 16969147756815248702], [5099504066399492044, "rfd", false, 3662369185957611140], [5180608563399064494, "tauri_macros", false, 5292189182309670084], [5610773616282026064, "build_script_build", false, 9531837170145944241], [5986029879202738730, "log", false, 11427039726786026724], [6662727387107639740, "objc", false, 6467137822367869871], [6819972695233064559, "os_info", false, 15479874289274662626], [7244058819997729774, "reqwest", false, 16420196634818481432], [8008191657135824715, "thiserror", false, 7177988751108916753], [8292277814562636972, "tauri_utils", false, 1793497724282020387], [8319709847752024821, "uuid", false, 10339700279421357674], [8589231650440095114, "embed_plist", false, 12511541688900018425], [9451456094439810778, "regex", false, 8192488953418035054], [9538054652646069845, "tokio", false, 719054119088736892], [9623796893764309825, "ignore", false, 15305602598360089149], [9689903380558560274, "serde", false, 2619042827247960912], [9920160576179037441, "getrandom", false, 11950520734185132455], [10563170702865159712, "flate2", false, 17434929156934136300], [10629569228670356391, "futures_util", false, 10282654967338091349], [11337703028400419576, "os_pipe", false, 17444341133808050076], [11601763207901161556, "tar", false, 13217407270768799607], [11693073011723388840, "raw_window_handle", false, 6731205830923669796], [12986574360607194341, "serde_repr", false, 5696920223490824587], [13208667028893622512, "rand", false, 6900219625556240075], [13625485746686963219, "anyhow", false, 3745609849202521122], [14162324460024849578, "tauri_runtime", false, 17910456659691279875], [14564311161534545801, "encoding_rs", false, 14243804298678071154], [14618885535728128396, "sys_locale", false, 13831165827278303807], [14923790796823607459, "indexmap", false, 1741609428373835735], [15367738274754116744, "serde_json", false, 6364790342758265420], [16066129441945555748, "bytes", false, 3082263952613915852], [16228250612241359704, "tauri_runtime_wry", false, 17578330311998545141], [16371049426708177877, "cocoa", false, 10017781449264370349], [17155886227862585100, "glob", false, 16573883360962333953], [17278893514130263345, "state", false, 2395959566809431576]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-f2422928b3e9931f/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}