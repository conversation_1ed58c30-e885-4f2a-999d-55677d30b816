{"rustc": 4723136837156968084, "features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"not\", \"rustc_version\", \"sum\", \"try_into\", \"unwrap\"]", "declared_features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"generate-parsing-rs\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"nightly\", \"not\", \"peg\", \"rustc_version\", \"sum\", \"testing-helpers\", \"track-caller\", \"try_into\", \"unwrap\"]", "target": 12153973509411789784, "profile": 3033921117576893, "path": 8935390971038258885, "deps": [[3060637413840920116, "proc_macro2", false, 3703143559497602325], [14907448031486326382, "convert_case", false, 16443812526584395468], [17990358020177143287, "quote", false, 6117099007456061250], [18149961000318489080, "syn", false, 2953086674220351078]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-dedb8382af019954/dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}