{"rustc": 4723136837156968084, "features": "[\"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 5347358027863023418, "path": 681755271817275928, "deps": [[2924422107542798392, "libc", false, 6993332256108622361], [3007252114546291461, "tao", false, 7824012669924346965], [3150220818285335163, "url", false, 1344501035966576598], [3722963349756955755, "once_cell", false, 18347124574856344496], [4405182208873388884, "http", false, 17871174199404994561], [5986029879202738730, "log", false, 11427039726786026724], [6662727387107639740, "objc", false, 6467137822367869871], [6839889263267961860, "objc_id", false, 11694158124407079786], [8008191657135824715, "thiserror", false, 7177988751108916753], [8391357152270261188, "build_script_build", false, 214617251499055836], [9689903380558560274, "serde", false, 2619042827247960912], [11358553063310754093, "block", false, 13881483122640046810], [15242033613032491251, "core_graphics", false, 14509311118706974202], [15367738274754116744, "serde_json", false, 6364790342758265420], [16371049426708177877, "cocoa", false, 10017781449264370349]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wry-f4f768b6cb9a3c2d/dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}