{"rustc": 4723136837156968084, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 5347358027863023418, "path": 6286470556076432891, "deps": [[966925859616469517, "ahash", false, 17798026255638301847], [9150530836556604396, "allocator_api2", false, 9376330656648454533]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-13dbba3c7bb140a3/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}