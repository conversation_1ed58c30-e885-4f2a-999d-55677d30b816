{"rustc": 4723136837156968084, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 3033921117576893, "path": 8533237773945268248, "deps": [[996810380461694889, "sqlx_core", false, 15077168357729521117], [2713742371683562785, "syn", false, 11713761178691210934], [3060637413840920116, "proc_macro2", false, 3703143559497602325], [15733334431800349573, "sqlx_macros_core", false, 6084007401764142307], [17990358020177143287, "quote", false, 6117099007456061250]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-2112e7c96053df2d/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}