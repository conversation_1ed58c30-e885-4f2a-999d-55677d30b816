{"rustc": 4723136837156968084, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17878142613068009629, "path": 8304378074009131504, "deps": [[5103565458935487, "futures_io", false, 8327948674027482686], [1615478164327904835, "pin_utils", false, 11137536884893729013], [1906322745568073236, "pin_project_lite", false, 10412852825593021829], [3129130049864710036, "memchr", false, 13226132519458249420], [6955678925937229351, "slab", false, 9192195096208637681], [7013762810557009322, "futures_sink", false, 11456496433767073267], [7620660491849607393, "futures_core", false, 12914485325312889039], [16240732885093539806, "futures_task", false, 5324428705623261714]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-1c1ee6c5c76ebb65/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}