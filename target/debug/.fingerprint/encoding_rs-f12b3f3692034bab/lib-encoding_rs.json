{"rustc": 4723136837156968084, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 5347358027863023418, "path": 3875226525057908900, "deps": [[10411997081178400487, "cfg_if", false, 9551478225892533772]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/encoding_rs-f12b3f3692034bab/dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}