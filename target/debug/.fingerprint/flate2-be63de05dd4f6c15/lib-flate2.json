{"rustc": 4723136837156968084, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 8276155916380437441, "path": 77767172520238610, "deps": [[4675849561795547236, "miniz_oxide", false, 14238262822613169293], [5466618496199522463, "crc32fast", false, 16242460263786373927]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-be63de05dd4f6c15/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}